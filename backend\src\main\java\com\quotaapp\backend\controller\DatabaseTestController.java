package com.quotaapp.backend.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.quotaapp.backend.model.User;
import com.quotaapp.backend.repository.primary.UserRepository;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
public class DatabaseTestController {

    @Autowired
    private UserRepository userRepository;

    @GetMapping("/db-connection")
    public ResponseEntity<Map<String, Object>> testDatabaseConnection() {
        Map<String, Object> response = new HashMap<>();

        try {
            // Create a test user
            User testUser = User.builder()
                .email("<EMAIL>")
                .password("test_password")
                .build();

            // Save the user to the database
            User savedUser = userRepository.save(testUser);

            // Delete the test user (cleanup)
            userRepository.delete(savedUser);

            response.put("status", "success");
            response.put("message", "Database connection successful");
            response.put("testUser", savedUser);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Database connection failed");
            response.put("error", e.getMessage());

            return ResponseEntity.status(500).body(response);
        }
    }
}