@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: Space Grotesk, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --radius: 0.5rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

:root {
  --radius: 0.5rem;
  --background: oklch(0.98 0.01 95.10);
  --foreground: oklch(0.34 0.03 95.72);
  --card: oklch(0.98 0.01 95.10);
  --card-foreground: oklch(0.19 0.00 106.59);
  --popover: oklch(1.00 0 0);
  --popover-foreground: oklch(0.27 0.02 98.94);
  --primary: #9e1d20;
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.92 0.01 92.99);
  --secondary-foreground: oklch(0.43 0.02 98.60);
  --muted: oklch(0.93 0.02 90.24);
  --muted-foreground: oklch(0.61 0.01 97.42);
  --accent: oklch(0.92 0.01 92.99);
  --accent-foreground: oklch(0.27 0.02 98.94);
  --destructive: oklch(0.19 0.00 106.59);
  --border: oklch(0.88 0.01 97.36);
  --input: oklch(0.76 0.02 98.35);
  --ring: oklch(0.59 0.17 253.06);
  --chart-1: oklch(0.56 0.13 43.00);
  --chart-2: oklch(0.69 0.16 290.41);
  --chart-3: oklch(0.88 0.03 93.13);
  --chart-4: oklch(0.88 0.04 298.18);
  --chart-5: oklch(0.56 0.13 42.06);
  --sidebar: oklch(0.97 0.01 98.88);
  --sidebar-foreground: oklch(0.36 0.01 106.65);
  --sidebar-primary: oklch(0.62 0.14 39.04);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.92 0.01 92.99);
  --sidebar-accent-foreground: oklch(0.33 0 0);
  --sidebar-border: oklch(0.94 0 0);
  --sidebar-ring: oklch(0.77 0 0);
  --destructive-foreground: oklch(1.00 0 0);
  --font-sans: Space Grotesk, serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 0.06;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0.05em;
  --spacing: 0.25rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.03);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.03);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.06), 0 1px 2px -1px hsl(0 0% 0% / 0.06);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.06), 0 1px 2px -1px hsl(0 0% 0% / 0.06);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.06), 0 2px 4px -1px hsl(0 0% 0% / 0.06);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.06), 0 4px 6px -1px hsl(0 0% 0% / 0.06);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.06), 0 8px 10px -1px hsl(0 0% 0% / 0.06);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.15);
  --tracking-normal: 0.05em;
}

.dark {
  --background: oklch(0.27 0.00 106.64);
  --foreground: oklch(0.81 0.01 93.01);
  --card: oklch(0.27 0.00 106.64);
  --card-foreground: oklch(0.98 0.01 95.10);
  --popover: oklch(0.31 0.00 106.60);
  --popover-foreground: oklch(0.92 0.00 106.48);
  --primary: #9e1d20;
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.98 0.01 95.10);
  --secondary-foreground: oklch(0.31 0.00 106.60);
  --muted: oklch(0.22 0.00 106.71);
  --muted-foreground: oklch(0.77 0.02 99.07);
  --accent: oklch(0.21 0.01 95.42);
  --accent-foreground: oklch(0.97 0.01 98.88);
  --destructive: oklch(0.64 0.21 25.33);
  --border: oklch(0.36 0.01 106.89);
  --input: oklch(0.43 0.01 100.22);
  --ring: oklch(0.59 0.17 253.06);
  --chart-1: oklch(0.56 0.13 43.00);
  --chart-2: oklch(0.69 0.16 290.41);
  --chart-3: oklch(0.21 0.01 95.42);
  --chart-4: oklch(0.31 0.05 289.32);
  --chart-5: oklch(0.56 0.13 42.06);
  --sidebar: oklch(0.24 0.00 67.71);
  --sidebar-foreground: oklch(0.81 0.01 93.01);
  --sidebar-primary: oklch(0.33 0 0);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.17 0.00 106.62);
  --sidebar-accent-foreground: oklch(0.81 0.01 93.01);
  --sidebar-border: oklch(0.94 0 0);
  --sidebar-ring: oklch(0.77 0 0);
  --destructive-foreground: oklch(1.00 0 0);
  --radius: 0.5rem;
  --font-sans: Space Grotesk, serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 0.06;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0.05em;
  --spacing: 0.25rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.03);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.03);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.06), 0 1px 2px -1px hsl(0 0% 0% / 0.06);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.06), 0 1px 2px -1px hsl(0 0% 0% / 0.06);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.06), 0 2px 4px -1px hsl(0 0% 0% / 0.06);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.06), 0 4px 6px -1px hsl(0 0% 0% / 0.06);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.06), 0 8px 10px -1px hsl(0 0% 0% / 0.06);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.15);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}