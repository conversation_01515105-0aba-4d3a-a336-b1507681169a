"use client";

import { useEffect, useState } from "react";
import { ModeToggle } from "@/components/mode-toggle";
import { MagicBackButton } from "@/components/ui/magic-back-button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Car,
  User,
  Fuel,
  ChevronRight,
  Edit
} from "lucide-react";
import { apiService } from "@/services/api";
import { useAuth } from "@/hooks/useAuth";
import { Loading } from "@/components/ui/loading";
import { ErrorMessage } from "@/components/ui/error-message";
import Link from "next/link";
import { Vehicle } from "@/types/vehicle";
import { toast } from "sonner";
import { QuotaCard } from "@/components/quota-card";
import { ConsumptionHistory } from "@/components/consumption-history";



export default function Dashboard() {
  const { isAuthenticated, isLoading: authLoading, error: authError, user } = useAuth();
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [quotaData, setQuotaData] = useState<any[]>([]);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isQuotaLoading, setIsQuotaLoading] = useState(true);
  const [isTransactionsLoading, setIsTransactionsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quotaError, setQuotaError] = useState<string | null>(null);
  const [transactionsError, setTransactionsError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch vehicle data when authenticated
  useEffect(() => {
    const fetchVehicleData = async () => {
      if (!isAuthenticated) return;

      try {
        setIsLoading(true);
        const response = await apiService.getVehicleDetails();

        if (response.error) {
          setError(response.error);
          toast.error("Failed to load vehicle data");
        } else if (response.data) {
          setVehicles(Array.isArray(response.data) ? response.data : response.data.vehicles || []);
          setError(null);
        }
      } catch (err) {
        console.error("Error fetching vehicle data:", err);
        setError("Failed to load vehicle data. Please try again.");
        toast.error("Failed to load vehicle data");
      } finally {
        setIsLoading(false);
      }
    };

    if (isAuthenticated && !authLoading) {
      fetchVehicleData();
    }
  }, [isAuthenticated, authLoading]);

  // Fetch quota data when authenticated
  useEffect(() => {
    const fetchQuotaData = async () => {
      if (!isAuthenticated) return;

      try {
        setIsQuotaLoading(true);
        const response = await apiService.getFuelQuota();

        if (response.error) {
          setQuotaError(response.error);
          toast.error("Failed to load quota data");
        } else if (response.data) {
          setQuotaData(Array.isArray(response.data) ? response.data : [response.data]);
          setQuotaError(null);
        }
      } catch (err) {
        console.error("Error fetching quota data:", err);
        setQuotaError("Failed to load quota data. Please try again.");
        toast.error("Failed to load quota data");
      } finally {
        setIsQuotaLoading(false);
      }
    };

    if (isAuthenticated && !authLoading) {
      fetchQuotaData();
    }
  }, [isAuthenticated, authLoading]);

  // Fetch consumption history when authenticated
  useEffect(() => {
    const fetchConsumptionHistory = async () => {
      if (!isAuthenticated) return;

      try {
        setIsTransactionsLoading(true);
        const response = await apiService.getConsumptionHistory(currentPage, 5);

        if (response.error) {
          setTransactionsError(response.error);
          toast.error("Failed to load consumption history");
        } else if (response.data) {
          setTransactions(response.data.content || []);
          setTotalPages(response.data.totalPages || 1);
          setTransactionsError(null);
        }
      } catch (err) {
        console.error("Error fetching consumption history:", err);
        setTransactionsError("Failed to load consumption history. Please try again.");
        toast.error("Failed to load consumption history");
      } finally {
        setIsTransactionsLoading(false);
      }
    };

    if (isAuthenticated && !authLoading) {
      fetchConsumptionHistory();
    }
  }, [isAuthenticated, authLoading, currentPage]);

  // Handle page change for consumption history
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Combine loading states
  const isPageLoading = authLoading || isLoading || isQuotaLoading || isTransactionsLoading;
  // Combine error states
  const pageError = authError || error;

  return (
    <div className="flex flex-col min-h-svh w-full relative bg-background">

      {/* Theme toggle button */}
      <div className="fixed bottom-6 right-6 z-50">
        <ModeToggle />
      </div>

      {/* Loading state */}
      {isPageLoading && (
        <div className="flex-1 flex items-center justify-center">
          <Loading text="Loading dashboard..." />
        </div>
      )}

      {/* Error state */}
      {!isPageLoading && pageError && (
        <div className="flex-1 flex items-center justify-center">
          <ErrorMessage
            message={pageError}
            onRetry={() => window.location.reload()}
          />
        </div>
      )}

      {/* Main content - only show when not loading and no errors */}
      {!isPageLoading && !pageError && (
      <div className="flex flex-1 pt-16 px-4 md:px-8 pb-8">
        <div className="w-full max-w-7xl mx-auto space-y-6">
          {/* Dashboard Header */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="flex items-center gap-3">
              <MagicBackButton backLink="/" />
              <div>
                <h1 className="text-2xl md:text-3xl font-bold">Dashboard</h1>
                <p className="text-muted-foreground">Manage your fuel quota and vehicle information</p>
              </div>
            </div>
          </div>

          {/* Main Dashboard Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Profile Card */}
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <User className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">Profile Information</CardTitle>
                  </div>
                  <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
                    <Link href="/dashboard/profile/edit">
                      <Edit className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Full Name</p>
                    <p>{user?.fullName || "Not available"}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">NIC Number</p>
                    <p>{user?.nicNumber || "Not available"}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Contact Number</p>
                    <p>{user?.contactNumber || "Not available"}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Email</p>
                    <p>{user?.email || "Not available"}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Address</p>
                    <p className="text-sm">{user?.address || "Not available"}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Vehicles Information Card */}
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Car className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">Your Vehicles</CardTitle>
                  </div>
                  <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
                    <Link href="/dashboard/vehicles">
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {vehicles.length > 0 ? (
                    <>
                      {vehicles.slice(0, 2).map((vehicle, index) => (
                        <div key={vehicle.id} className={`pb-3 ${index < vehicles.slice(0, 2).length - 1 ? 'border-b border-border' : ''}`}>
                          <div className="flex justify-between items-start mb-1">
                            <p className="font-medium">{vehicle.make} {vehicle.model}</p>
                            <p className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">{vehicle.fuelType}</p>
                          </div>
                          <p className="text-sm text-muted-foreground mb-1">
                            {vehicle.registrationNumber}
                          </p>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Fuel className="h-3 w-3" />
                            <span>{vehicle.quota.remainingQuota} / {vehicle.quota.totalQuota} {vehicle.quota.quotaUnit}</span>
                          </div>
                        </div>
                      ))}

                      {vehicles.length > 2 && (
                        <p className="text-xs text-muted-foreground text-center">
                          +{vehicles.length - 2} more vehicles
                        </p>
                      )}
                    </>
                  ) : (
                    <div className="py-4 text-center">
                      <p className="text-sm text-muted-foreground">No vehicles found</p>
                      <p className="text-xs text-muted-foreground mt-1">Add a vehicle to get started</p>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex flex-col gap-2">
                <Button variant="default" size="sm" className="w-full" asChild>
                  <Link href="/dashboard/vehicles">
                    Manage Vehicles
                  </Link>
                </Button>
                <Button variant="outline" size="sm" className="w-full" asChild>
                  <Link href="/dashboard/vehicles/add">
                    Add New Vehicle
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Fuel Quota Card */}
            {quotaData.length > 0 && (
              <QuotaCard
                vehicleId={parseInt(quotaData[0].vehicleId)}
                registrationNumber={quotaData[0].registrationNumber}
                allocatedAmount={parseFloat(quotaData[0].allocatedAmount)}
                remainingAmount={parseFloat(quotaData[0].remainingAmount)}
                allocationDate={quotaData[0].allocationDate}
                expiryDate={quotaData[0].expiryDate}
                quotaStatus={quotaData[0].quotaStatus}
                nextAllocationDate={quotaData[0].nextAllocationDate}
              />
            )}

            {/* Consumption History Card */}
            <ConsumptionHistory
              transactions={transactions}
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              loading={isTransactionsLoading}
            />
          </div>


        </div>
      </div>
      )}
    </div>
  );
}