Stack trace:
Frame         Function      Args
0007FFFF4C00  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF3B00) msys-2.0.dll+0x1FEBA
0007FFFF4C00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF4ED8) msys-2.0.dll+0x67F9
0007FFFF4C00  000210046832 (000210285FF9, 0007FFFF4AB8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF4C00  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF4C00  0002100690B4 (0007FFFF4C10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF4EE0  00021006A49D (0007FFFF4C10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDD5160000 ntdll.dll
7FFDD4620000 KERNEL32.DLL
7FFDD29C0000 KERNELBASE.dll
7FFDD4D40000 USER32.dll
7FFDD2D90000 win32u.dll
7FFDD4FE0000 GDI32.dll
7FFDD2380000 gdi32full.dll
7FFDD24C0000 msvcp_win.dll
7FFDD26F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDD4700000 advapi32.dll
7FFDD4020000 msvcrt.dll
7FFDD40E0000 sechost.dll
7FFDD41A0000 RPCRT4.dll
7FFDD19E0000 CRYPTBASE.DLL
7FFDD2DC0000 bcryptPrimitives.dll
7FFDD36D0000 IMM32.DLL
