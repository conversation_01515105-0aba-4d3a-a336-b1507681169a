import React, { useEffect, useState } from 'react';
import { Redirect } from 'expo-router';
import { useAuth } from '../src/context/AuthContext';
import { WelcomeScreen } from '../src/screens/auth';

export default function Index() {
  const { isAuthenticated, isLoading } = useAuth();
  const [showWelcome, setShowWelcome] = useState(true);

  useEffect(() => {
    // Hide welcome screen after auth check is complete
    if (!isLoading) {
      const timer = setTimeout(() => {
        setShowWelcome(false);
      }, 2500);
      return () => clearTimeout(timer);
    }
  }, [isLoading]);

  // Show welcome screen on app start
  if (showWelcome || isLoading) {
    return (
      <WelcomeScreen onComplete={() => setShowWelcome(false)} />
    );
  }

  // Redirect based on authentication status
  if (isAuthenticated) {
    return <Redirect href="/(tabs)/index" />;
  } else {
    return <Redirect href="/(auth)/index" />;
  }
}
