{"name": "quota.app", "version": "1.0.0", "private": true, "workspaces": ["frontend-vehicle", "frontend-station", "frontend-admin"], "scripts": {"dev:vehicle": "cd frontend-vehicle && bun run dev", "dev:station": "cd frontend-station && bun run dev", "dev:admin": "cd frontend-admin && bun run dev", "dev:all": "concurrently \"bun run dev:vehicle\" \"bun run dev:station\" \"bun run dev:admin\"", "dev:vs": "concurrently \"bun run dev:vehicle\" \"bun run dev:station\"", "dev:va": "concurrently \"bun run dev:vehicle\" \"bun run dev:admin\"", "dev:sa": "concurrently \"bun run dev:station\" \"bun run dev:admin\""}, "devDependencies": {"concurrently": "^9.1.2"}, "dependencies": {"@radix-ui/react-collection": "^1.1.6", "@radix-ui/react-compose-refs": "^1.1.2", "@radix-ui/react-context": "^1.1.2", "@radix-ui/react-menu": "^2.1.14", "@radix-ui/react-primitive": "^2.1.2", "@radix-ui/react-roving-focus": "^1.1.9", "@radix-ui/react-slot": "^1.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^3.2.0"}}